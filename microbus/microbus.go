package microbus

import (
	"time"

	"foxess.beech/config"
	"foxess.beech/logger"

	fmicrobus "foxess.cloud/fmicrobus"
	"github.com/rs/zerolog"
)

// RealtimeMessage 表示实时数据消息
type RealtimeMessage struct {
	Resource  string
	Payload   []byte
	Timestamp time.Time
}

// Client 封装microbus功能
type Client struct {
	micbus  *fmicrobus.MicroBus
	config  *config.MicroBusConfig
	logger  zerolog.Logger
	msgChan chan *RealtimeMessage
	started bool
}

// NewClient 创建新的microbus客户端
func NewClient(cfg *config.MicroBusConfig) *Client {
	log := logger.GetLogger()

	micbus := fmicrobus.New(cfg.Brokers)

	client := &Client{
		micbus:  micbus,
		config:  cfg,
		logger:  log,
		msgChan: make(chan *RealtimeMessage, cfg.QueueSize),
		started: false,
	}

	return client
}

// Start 启动MicroBus客户端，开始订阅实时数据
func (c *Client) Start() error {
	if c.started {
		return nil
	}

	c.logger.Info().Msg("Starting MicroBus realtime data subscription")

	// 订阅配置中的实时数据topics
	for _, pattern := range c.config.Topics {
		c.logger.Info().Str("pattern", pattern).Msg("Subscribing to realtime data pattern")

		// 使用WaitAsync订阅实时数据
		c.micbus.WaitAsync(pattern, func(mmsg *fmicrobus.MicroMessage) {
			resource := mmsg.GetResource()
			payload := mmsg.GetPayload()
			timestampInt := mmsg.GetTimestamp()
			timestamp := time.Unix(timestampInt, 0)

			c.logger.Debug().Str("resource", resource).Time("timestamp", timestamp).Int("payload_size", len(payload)).Msg("Received realtime data from microbus")

			// 将消息发送到通道
			msg := &RealtimeMessage{
				Resource:  resource,
				Payload:   payload,
				Timestamp: timestamp,
			}

			select {
			case c.msgChan <- msg:
				// 消息成功发送到通道
			default:
				c.logger.Warn().Str("resource", resource).Msg("Message channel is full, dropping message")
			}
		}, c.config.QueueSize)
	}

	c.started = true
	c.logger.Info().Strs("patterns", c.config.Topics).Msg("MicroBus realtime data subscription started successfully")
	return nil
}

// GetMessageChannel 返回实时数据消息通道
func (c *Client) GetMessageChannel() <-chan *RealtimeMessage {
	return c.msgChan
}

// Close 关闭客户端和消息通道
func (c *Client) Close() {
	if c.msgChan != nil {
		close(c.msgChan)
	}
}

// GetMicroBus 返回底层的MicroBus实例
func (c *Client) GetMicroBus() *fmicrobus.MicroBus {
	return c.micbus
}
