package main

import (
	"context"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"foxess.beech/config"
	"foxess.beech/forwarder"
	"foxess.beech/logger"
	"foxess.beech/microbus"
	"foxess.beech/mqtt"
)

func main() {
	cfg, err := config.Load()
	if err != nil {
		panic("Failed to load configuration: " + err.<PERSON><PERSON>r())
	}

	logger.Init(cfg)
	log := logger.GetLogger()

	log.Info().Msg("Starting beech (High Performance Forwarder)")

	microbusClient := microbus.NewClient(&cfg.MicroBus)

	// 创建MQTT客户端
	mqttClient, err := mqtt.NewClient(&cfg.MQTT)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create MQTT client")
	}

	// 创建消息桥接器
	messageBridge := forwarder.NewMessageBridge(microbusClient, mqttClient, &cfg.Hub)

	// 连接MQTT客户端
	if err := mqttClient.Connect(); err != nil {
		log.Fatal().Err(err).Msg("Failed to connect to MQTT broker")
	}
	defer func() {
		mqttClient.Disconnect()
		mqttClient.Close()
	}()

	// 启动消息桥接服务
	if err := messageBridge.Start(); err != nil {
		log.Fatal().Err(err).Msg("Failed to start message bridge")
	}
	defer func() {
		messageBridge.Stop()
		microbusClient.Close()
	}()

	log.Info().Msg("beech started successfully")

	// Wait for interrupt signal to gracefully shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Wait for shutdown signal
	<-sigChan
	log.Info().Msg("Shutdown signal received, starting graceful shutdown")

	// Create shutdown context with timeout
	shutdownCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Graceful shutdown
	var wg sync.WaitGroup

	// 优雅关闭 MQTT 客户端
	wg.Add(1)
	go func() {
		defer wg.Done()
		log.Info().Msg("Closing MQTT client")
		mqttClient.Disconnect()
		mqttClient.Close()
	}()

	// 优雅关闭 MicroBus 客户端
	wg.Add(1)
	go func() {
		defer wg.Done()
		log.Info().Msg("Closing MicroBus client")
		microbusClient.Close()
	}()

	// 优雅关闭消息桥接器
	wg.Add(1)
	go func() {
		defer wg.Done()
		log.Info().Msg("Stopping message bridge")
		messageBridge.Stop()
	}()

	// Wait for all components to shutdown or timeout
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		log.Info().Msg("Graceful shutdown completed")
	case <-shutdownCtx.Done():
		log.Warn().Msg("Shutdown timeout exceeded, forcing exit")
	}

	log.Info().Msg("beech stopped")
}
