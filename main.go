package main

import (
	"context"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"foxess.beech/config"
	"foxess.beech/forwarder"
	"foxess.beech/logger"
	"foxess.beech/microbus"
	"foxess.beech/mqtt"
)

func main() {
	cfg, err := config.Load()
	if err != nil {
		panic("Failed to load configuration: " + err.<PERSON><PERSON>r())
	}

	logger.Init(cfg)
	log := logger.GetLogger()

	log.Info().Msg("Starting beech (High Performance Forwarder)")

	microbusClient := microbus.NewClient(&cfg.MicroBus)

	// 创建MQTT客户端
	mqttClient, err := mqtt.NewClient(&cfg.MQTT)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create MQTT client")
	}

	// 创建消息桥接器
	messageBridge := forwarder.NewMessageBridge(microbusClient, mqttClient, &cfg.Hub)

	// 连接MQTT客户端
	if err := mqttClient.Connect(); err != nil {
		log.Fatal().Err(err).Msg("Failed to connect to MQTT broker")
	}
	defer mqttClient.Disconnect()

	// 启动消息桥接服务
	if err := messageBridge.Start(); err != nil {
		log.Fatal().Err(err).Msg("Failed to start message bridge")
	}
	defer messageBridge.Stop()

	log.Info().Msg("beech started successfully")

	// Wait for interrupt signal to gracefully shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Wait for shutdown signal
	<-sigChan
	log.Info().Msg("Shutdown signal received, starting graceful shutdown")

	// Create shutdown context with timeout
	shutdownCtx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// Graceful shutdown
	var wg sync.WaitGroup

	// MicroBus client doesn't need explicit stop since it has no close method
	log.Info().Msg("MicroBus client will be cleaned up automatically")

	// Wait for all components to shutdown or timeout
	done := make(chan struct{})
	go func() {
		wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		log.Info().Msg("Graceful shutdown completed")
	case <-shutdownCtx.Done():
		log.Warn().Msg("Shutdown timeout exceeded, forcing exit")
	}

	log.Info().Msg("beech stopped")
}
