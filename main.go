package main

import (
	"context"
	"os"
	"os/signal"
	"syscall"
	"time"

	"foxess.beech/config"
	"foxess.beech/forwarder"
	"foxess.beech/logger"
	"foxess.beech/microbus"
	"foxess.beech/mqtt"
	"github.com/rs/zerolog"
)

func main() {
	cfg, err := config.Load()
	if err != nil {
		panic("Failed to load configuration: " + err.Error())
	}

	logger.Init(cfg)
	log := logger.GetLogger()

	log.Info().Msg("Starting beech (High Performance Forwarder)")

	microbusClient := microbus.NewClient(&cfg.MicroBus)

	// 创建MQTT客户端
	mqttClient, err := mqtt.NewClient(&cfg.MQTT)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create MQTT client")
	}

	// 创建消息桥接器
	messageBridge := forwarder.NewMessageBridge(microbusClient, mqttClient, &cfg.Hub)

	// 连接MQTT客户端
	if err := mqttClient.Connect(); err != nil {
		log.Fatal().Err(err).Msg("Failed to connect to MQTT broker")
	}
	defer mqttClient.Disconnect()

	// 启动消息桥接服务
	if err := messageBridge.Start(); err != nil {
		log.Fatal().Err(err).Msg("Failed to start message bridge")
	}
	defer messageBridge.Stop()

	log.Info().Msg("beech started successfully")

	// Wait for interrupt signal to gracefully shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Wait for shutdown signal
	<-sigChan
	log.Info().Msg("Shutdown signal received, starting graceful shutdown")

	// 优雅关闭所有组件
	gracefulShutdown(log, mqttClient, microbusClient)

	log.Info().Msg("beech stopped")
}

// gracefulShutdown 优雅关闭所有组件
func gracefulShutdown(log zerolog.Logger, mqttClient *mqtt.Client, microbusClient *microbus.Client) {
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	log.Info().Msg("Closing client channels")

	// 关闭通道，让 goroutine 能够正常退出
	mqttClient.Close()
	microbusClient.Close()

	// 等待一小段时间让 goroutine 处理完成
	select {
	case <-time.After(2 * time.Second):
		log.Info().Msg("Graceful shutdown completed")
	case <-ctx.Done():
		log.Warn().Msg("Shutdown timeout exceeded")
	}
}
