# beech Makefile

.PHONY: build test clean run docker-build docker-run dev-up dev-down help \
        example mqtt-example modbus-example multi-example integration check-examples \
        fmt lint deps check-deps test-suite dev-setup clean-all status

# Default target
help:
	@echo "🚀 beech (High Performance Forwarder) - Available Commands"
	@echo "========================================================"
	@echo ""
	@echo "📦 Build & Run:"
	@echo "  build        - Build the beech binary"
	@echo "  run          - Run beech locally (builds first)"
	@echo "  clean        - Clean build artifacts"
	@echo ""
	@echo "🧪 Testing:"
	@echo "  test         - Run Go unit tests"
	@echo "  test-suite   - Run complete example test suite"
	@echo "  check-examples - Check prerequisites for examples"
	@echo ""
	@echo "🐳 Docker:"
	@echo "  docker-build - Build Docker image"
	@echo "  docker-run   - Run with Docker Compose"
	@echo "  dev-up       - Start development environment (Kafka + MQTT + UI)"
	@echo "  dev-down     - Stop development environment"
	@echo ""
	@echo "🔧 Examples:"
	@echo "  example      - Run MicroBus producer example"
	@echo "  mqtt-example - Run MQTT client example"
	@echo "  modbus-example - Run Modbus command examples"
	@echo "  multi-example - Run multi-device example"
	@echo "  integration  - Run complete integration test"
	@echo ""
	@echo "🛠️ Development:"
	@echo "  fmt          - Format code"
	@echo "  lint         - Lint code"
	@echo "  deps         - Install/update dependencies"
	@echo "  check-deps   - Check if required tools are installed"
	@echo "  help         - Show this help message"

# Build the application
build:
	@echo "🔨 Building beech..."
	@go version
	go build -ldflags="-s -w" -o beech .
	@echo "✅ Build completed: ./beech"

# Run tests
test:
	@echo "🧪 Running Go unit tests..."
	go test -v -race -coverprofile=coverage.out ./...
	@echo "✅ Tests completed"
	@if [ -f coverage.out ]; then \
		echo "📊 Test coverage:"; \
		go tool cover -func=coverage.out | tail -1; \
	fi

# Clean build artifacts
clean:
	@echo "🧹 Cleaning build artifacts..."
	rm -f beech
	rm -f coverage.out
	go clean -cache -testcache -modcache
	@echo "✅ Clean completed"

# Run locally (requires MicroBus/Kafka and MQTT to be running)
run: build
	@echo "🚀 Running beech locally..."
	@echo "📋 Prerequisites:"
	@echo "   - Kafka running on localhost:9092"
	@echo "   - MQTT running on localhost:1883"
	@echo "   - Environment variables configured"
	@echo ""
	@echo "💡 Tip: Run 'make dev-up' first to start required services"
	@echo "💡 Tip: Run 'make check-examples' to verify setup"
	@echo ""
	./beech

# Build Docker image
docker-build:
	@echo "🐳 Building Docker image..."
	docker build -t beech:latest .
	@echo "✅ Docker image built: beech:latest"
	@docker images | grep beech

# Run with Docker Compose
docker-run: docker-build
	@echo "🐳 Starting beech with Docker Compose..."
	docker-compose up -d
	@echo "✅ beech started"
	@echo "📋 Use 'make dev-down' to stop all services"
	@echo "📋 Use 'docker-compose logs -f beech' to view logs"

# Start development environment
dev-up:
	@echo "🚀 Starting development environment..."
	docker-compose up -d kafka zookeeper mosquitto kafka-ui
	@echo "⏳ Waiting for services to be ready..."
	@sleep 10
	@echo "✅ Development environment started"
	@echo ""
	@echo "📋 Available services:"
	@echo "   🌐 Kafka UI: http://localhost:8080"
	@echo "   📨 MQTT: localhost:1883"
	@echo "   📡 Kafka: localhost:9092"
	@echo ""
	@echo "💡 Next steps:"
	@echo "   1. Run beech: make run"
	@echo "   2. Test examples: make test-suite"

# Stop development environment
dev-down:
	@echo "🛑 Stopping development environment..."
	docker-compose down
	@echo "✅ Development environment stopped"

# Show service status
status:
	@echo "📊 Service Status:"
	@echo "=================="
	@docker-compose ps

# Check prerequisites for examples
check-examples:
	@echo "🔍 Checking prerequisites for examples..."
	cd examples && go run main.go check

# Run complete example test suite
test-suite:
	@echo "🧪 Running complete example test suite..."
	cd examples && ./test.sh

# Run MicroBus producer example
example:
	@echo "📡 Running MicroBus producer example..."
	cd examples && go run main.go microbus

# Run MQTT client example
mqtt-example:
	@echo "📨 Running MQTT client example..."
	cd examples && go run main.go mqtt

# Run Modbus command examples
modbus-example:
	@echo "🔧 Running Modbus command examples..."
	cd examples && go run main.go modbus

# Run multi-device example
multi-example:
	@echo "🏭 Running multi-device example..."
	cd examples && go run main.go multi

# Run complete integration test
integration:
	@echo "🔄 Running complete integration test..."
	cd examples && go run main.go test

# Check if required development tools are installed
check-deps:
	@echo "🔍 Checking required tools..."
	@command -v go >/dev/null 2>&1 || { echo "❌ Go is not installed"; exit 1; }
	@command -v docker >/dev/null 2>&1 || { echo "❌ Docker is not installed"; exit 1; }
	@command -v docker-compose >/dev/null 2>&1 || { echo "❌ Docker Compose is not installed"; exit 1; }
	@echo "✅ All required tools are installed"

# Format code
fmt:
	@echo "🎨 Formatting code..."
	go fmt ./...
	gofmt -s -w .
	@echo "✅ Code formatted"

# Lint code (install golangci-lint if not present)
lint:
	@echo "🔍 Linting code..."
	@if ! command -v golangci-lint >/dev/null 2>&1; then \
		echo "Installing golangci-lint..."; \
		go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest; \
	fi
	golangci-lint run
	@echo "✅ Linting completed"

# Install/update dependencies
deps:
	@echo "📦 Installing dependencies..."
	go mod download
	go mod tidy
	cd examples && go mod download && go mod tidy
	@echo "✅ Dependencies installed"

# Quick development setup
dev-setup: check-deps deps
	@echo "🚀 Setting up development environment..."
	@echo "✅ Development setup completed"
	@echo ""
	@echo "Next steps:"
	@echo "  1. Start services: make dev-up"
	@echo "  2. Run beech: make run"
	@echo "  3. Test examples: make test-suite"

# Complete cleanup
clean-all: clean dev-down
	@echo "🧹 Complete cleanup..."
	docker system prune -f
	@echo "✅ Complete cleanup finished"
