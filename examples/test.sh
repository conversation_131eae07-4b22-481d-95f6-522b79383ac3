#!/bin/bash

echo "🧪 beech Examples Test Suite"
echo "=========================="

# 检查前置条件
echo "1️⃣ Checking prerequisites..."
go run main.go check

echo ""
echo "2️⃣ Testing basic MQTT functionality..."
timeout 10s go run main.go mqtt || echo "MQTT test completed (timeout expected)"

echo ""
echo "3️⃣ Testing MicroBus functionality..."
timeout 10s go run main.go microbus || echo "MicroBus test completed (timeout expected)"

echo ""
echo "4️⃣ Testing Modbus commands..."
timeout 15s go run main.go modbus || echo "Modbus test completed (timeout expected)"

echo ""
echo "✅ Test suite completed!"
echo ""
echo "💡 Tips:"
echo "   - Make sure beech service is running: make run"
echo "   - Check beech logs for detailed message processing"
echo "   - Use 'go run main.go <command>' to run individual tests"
