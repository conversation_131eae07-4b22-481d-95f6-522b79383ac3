package mqtt

import (
	"encoding/json"
	"fmt"
	"strings"
	"sync"

	"foxess.beech/config"
	"foxess.beech/logger"
	"foxess.beech/types"

	"foxess.cloud/mop"
	"github.com/rs/zerolog"
)

// SettingRequestMessage 表示设置请求消息
type SettingRequestMessage struct {
	DevSN   string
	Request *types.SetRequest
}

// Client 表示MQTT客户端，基于 mop Engine
type Client struct {
	engine    *mop.Engine
	config    *config.MQTTConfig
	logger    zerolog.Logger
	connected bool
	mu        sync.RWMutex
	reqChan   chan *SettingRequestMessage
	started   bool
}

// NewClient 创建新的MQTT客户端，使用 mop Engine
func NewClient(cfg *config.MQTTConfig) (*Client, error) {
	log := logger.GetLogger()

	// 创建 mop 配置
	mopConfig := &mop.Config{
		ClientID: cfg.ClientID,
		Broker:   cfg.Broker,
		Username: cfg.Username,
		Password: cfg.Password,
	}

	// 创建 mop Engine
	engine := mop.New(mopConfig)

	client := &Client{
		engine:    engine,
		config:    cfg,
		logger:    log,
		reqChan:   make(chan *SettingRequestMessage, 100), // 缓冲通道
		started:   false,
		connected: false,
	}

	log.Info().Str("broker", cfg.Broker).Str("client_id", cfg.ClientID).Msg("MQTT client created with mop engine")

	return client, nil
}

// Connect connects to the MQTT broker using mop Engine
func (c *Client) Connect() error {
	c.logger.Info().Msg("Connecting to MQTT broker using mop engine")

	// 设置订阅处理器
	c.setupSubscriptions()

	// 启动 mop Engine
	c.engine.Run()

	c.mu.Lock()
	c.connected = true
	c.mu.Unlock()

	c.logger.Info().Msg("Connected to MQTT broker via mop engine")
	return nil
}

// setupSubscriptions 设置 MQTT 订阅处理器
func (c *Client) setupSubscriptions() {
	// 订阅设置请求 topic
	for _, topic := range c.config.SubscribeTopics {
		c.engine.Subscribe(topic, c.config.QoS, c.handleSettingRequest)
		c.logger.Info().Str("topic", topic).Msg("Subscribed to setting request topic")
	}
}

// handleSettingRequest 处理设置请求
func (c *Client) handleSettingRequest(ctx *mop.Context) {
	topic := ctx.Topic
	payload := ctx.Message.Payload()

	c.logger.Debug().Str("topic", topic).Str("payload", string(payload)).Msg("Received setting request")

	// 解析设备 SN
	devSN := c.extractDeviceSN(topic)
	if devSN == "" {
		c.logger.Error().Str("topic", topic).Msg("Failed to extract device SN from topic")
		return
	}

	// 解析请求数据
	var req types.SetRequest
	if err := json.Unmarshal(payload, &req); err != nil {
		c.logger.Error().Err(err).Str("topic", topic).Msg("Failed to unmarshal setting request")
		return
	}

	// 发送到请求通道
	reqMsg := &SettingRequestMessage{
		DevSN:   devSN,
		Request: &req,
	}

	select {
	case c.reqChan <- reqMsg:
		c.logger.Debug().Str("devSN", devSN).Uint32("id", req.ID).Msg("Setting request queued")
	default:
		c.logger.Error().Str("devSN", devSN).Uint32("id", req.ID).Msg("Setting request channel full, dropping request")
	}
}

// Disconnect disconnects from the MQTT broker
func (c *Client) Disconnect() {
	c.logger.Info().Msg("Disconnecting from MQTT broker")

	c.mu.Lock()
	c.connected = false
	c.mu.Unlock()

	// mop Engine 没有直接的 Disconnect 方法，这里只是标记为断开
	c.logger.Info().Msg("Disconnected from MQTT broker")
}

// IsConnected returns whether the client is connected
func (c *Client) IsConnected() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.connected
}

// Publish 发布消息到指定topic
func (c *Client) Publish(topic string, payload []byte) error {
	c.mu.RLock()
	connected := c.connected
	c.mu.RUnlock()

	if !connected {
		return fmt.Errorf("MQTT client not connected")
	}

	c.logger.Debug().Str("topic", topic).Int("payload_size", len(payload)).Msg("Publishing MQTT message")

	// 使用 mop Engine 发布消息
	c.engine.Publish(topic, c.config.QoS, false, payload)

	c.logger.Debug().Str("topic", topic).Msg("MQTT message published successfully")

	return nil
}

// extractDeviceSN 从topic中提取devSN
func (c *Client) extractDeviceSN(topic string) string {
	// 假设topic格式为: foxess/device/setting/v0/{devSN}/req
	// 或其他类似格式，根据实际情况调整
	parts := strings.Split(topic, "/")
	if len(parts) >= 5 {
		return parts[4] // devSN在第5个位置
	}
	return ""
}

// GetRequestChannel 返回设置请求消息通道
func (c *Client) GetRequestChannel() <-chan *SettingRequestMessage {
	return c.reqChan
}

// PublishSettingResponse 发布设置响应
func (c *Client) PublishSettingResponse(devSN string, resp *types.SetResponse) error {
	// 构建响应topic: foxess/device/setting/v0/{devSN}/resp
	respTopic := fmt.Sprintf("foxess/device/setting/v0/%s/resp", devSN)

	respData, err := json.Marshal(resp)
	if err != nil {
		c.logger.Error().Err(err).Str("devSN", devSN).Uint32("id", resp.ID).Msg("Failed to marshal setting response")
		return fmt.Errorf("failed to marshal response: %w", err)
	}

	if err := c.Publish(respTopic, respData); err != nil {
		c.logger.Error().Err(err).Str("devSN", devSN).Uint32("id", resp.ID).Msg("Failed to publish setting response")
		return fmt.Errorf("failed to publish response: %w", err)
	}

	c.logger.Info().Str("topic", respTopic).Str("devSN", devSN).Uint32("id", resp.ID).Str("data", resp.Frame).Msg("Published setting response")
	return nil
}

// Close 关闭客户端和请求通道
func (c *Client) Close() {
	if c.reqChan != nil {
		close(c.reqChan)
	}
}
