package mqtt

import (
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"foxess.beech/config"
	"foxess.beech/logger"
	"foxess.beech/types"

	mqtt "github.com/eclipse/paho.mqtt.golang"
	"github.com/rs/zerolog"
)

// SettingRequestMessage 表示设置请求消息
type SettingRequestMessage struct {
	DevSN   string
	Request *types.SetRequest
}

// Client 表示MQTT客户端
type Client struct {
	client    mqtt.Client
	config    *config.MQTTConfig
	logger    zerolog.Logger
	connected bool
	mu        sync.RWMutex
	reqChan   chan *SettingRequestMessage
	started   bool
}

// NewClient 创建新的MQTT客户端
func NewClient(cfg *config.MQTTConfig) (*Client, error) {
	log := logger.GetLogger()

	opts := mqtt.NewClientOptions()
	opts.AddBroker(cfg.Broker)
	opts.SetClientID(cfg.ClientID)

	if cfg.Username != "" {
		opts.SetUsername(cfg.Username)
	}
	if cfg.Password != "" {
		opts.SetPassword(cfg.Password)
	}

	opts.SetKeepAlive(60 * time.Second)
	opts.SetPingTimeout(10 * time.Second)
	opts.SetConnectTimeout(cfg.Timeout)
	opts.SetAutoReconnect(true)
	opts.SetMaxReconnectInterval(10 * time.Second)

	client := &Client{
		config:    cfg,
		logger:    log,
		reqChan:   make(chan *SettingRequestMessage, 100), // 缓冲通道
		started:   false,
		connected: false,
	}

	// Set connection handlers
	opts.SetOnConnectHandler(client.onConnect)
	opts.SetConnectionLostHandler(client.onConnectionLost)

	client.client = mqtt.NewClient(opts)

	log.Info().Str("broker", cfg.Broker).Str("client_id", cfg.ClientID).Msg("MQTT client created")

	return client, nil
}

// Connect connects to the MQTT broker
func (c *Client) Connect() error {
	c.logger.Info().Msg("Connecting to MQTT broker")

	token := c.client.Connect()
	if token.Wait() && token.Error() != nil {
		return fmt.Errorf("failed to connect to MQTT broker: %w", token.Error())
	}

	c.mu.Lock()
	c.connected = true
	c.mu.Unlock()

	c.logger.Info().Msg("Connected to MQTT broker")
	return nil
}

// Disconnect disconnects from the MQTT broker
func (c *Client) Disconnect() {
	c.logger.Info().Msg("Disconnecting from MQTT broker")

	c.mu.Lock()
	c.connected = false
	c.mu.Unlock()

	c.client.Disconnect(250)
	c.logger.Info().Msg("Disconnected from MQTT broker")
}

// IsConnected returns whether the client is connected
func (c *Client) IsConnected() bool {
	c.mu.RLock()
	defer c.mu.RUnlock()
	return c.connected && c.client.IsConnected()
}

// Publish 发布消息到指定topic
func (c *Client) Publish(topic string, payload []byte) error {
	c.mu.RLock()
	connected := c.connected
	c.mu.RUnlock()

	if !connected {
		return fmt.Errorf("MQTT client not connected")
	}

	c.logger.Debug().Str("topic", topic).Int("payload_size", len(payload)).Msg("Publishing MQTT message")

	token := c.client.Publish(topic, c.config.QoS, false, payload)
	if token.Wait() && token.Error() != nil {
		c.logger.Error().Err(token.Error()).Str("topic", topic).Msg("Failed to publish MQTT message")
		return token.Error()
	}

	c.logger.Debug().Str("topic", topic).Msg("MQTT message published successfully")

	return nil
}

// onConnect is called when the client connects
func (c *Client) onConnect(client mqtt.Client) {
	c.logger.Info().Msg("MQTT client connected")
	c.mu.Lock()
	c.connected = true
	c.mu.Unlock()

	// Subscribe to configured topics
	for _, topic := range c.config.SubscribeTopics {
		c.logger.Info().Str("topic", topic).Msg("Subscribing to MQTT topic")

		token := client.Subscribe(topic, c.config.QoS, c.onMessage)
		if token.Wait() && token.Error() != nil {
			c.logger.Error().Err(token.Error()).Str("topic", topic).Msg("Failed to subscribe to MQTT topic")
		} else {
			c.logger.Info().Str("topic", topic).Msg("Successfully subscribed to MQTT topic")
		}
	}
}

// onMessage handles incoming MQTT setting requests
func (c *Client) onMessage(client mqtt.Client, msg mqtt.Message) {
	c.logger.Debug().Str("topic", msg.Topic()).Int("payload_size", len(msg.Payload())).Msg("Received MQTT setting request")

	// 解析设置请求
	var req types.SetRequest
	if err := json.Unmarshal(msg.Payload(), &req); err != nil {
		c.logger.Error().Err(err).Str("topic", msg.Topic()).Msg("Failed to unmarshal setting request")
		return
	}

	// 从topic中提取devSN
	devSN := c.extractDevSNFromTopic(msg.Topic())
	if devSN == "" {
		c.logger.Error().Str("topic", msg.Topic()).Msg("Failed to extract devSN from topic")
		return
	}

	c.logger.Info().Str("devSN", devSN).Uint32("id", req.ID).Msg("Received setting request")

	// 将请求发送到通道
	reqMsg := &SettingRequestMessage{
		DevSN:   devSN,
		Request: &req,
	}

	select {
	case c.reqChan <- reqMsg:
		// 请求成功发送到通道
	default:
		c.logger.Warn().Str("devSN", devSN).Uint32("id", req.ID).Msg("Request channel is full, dropping request")
	}
}

// extractDevSNFromTopic 从topic中提取devSN
func (c *Client) extractDevSNFromTopic(topic string) string {
	// 假设topic格式为: foxess/device/setting/v0/{devSN}/req
	// 或其他类似格式，根据实际情况调整
	parts := strings.Split(topic, "/")
	if len(parts) >= 5 {
		return parts[4] // devSN在第5个位置
	}
	return ""
}

// onConnectionLost is called when the connection is lost
func (c *Client) onConnectionLost(client mqtt.Client, err error) {
	c.logger.Warn().Err(err).Msg("MQTT connection lost")
	c.mu.Lock()
	c.connected = false
	c.mu.Unlock()
}

// GetRequestChannel 返回设置请求消息通道
func (c *Client) GetRequestChannel() <-chan *SettingRequestMessage {
	return c.reqChan
}

// PublishSettingResponse 发布设置响应
func (c *Client) PublishSettingResponse(devSN string, resp *types.SetResponse) error {
	// 构建响应topic: foxess/device/setting/v0/{devSN}/resp
	respTopic := fmt.Sprintf("foxess/device/setting/v0/%s/resp", devSN)

	respData, err := json.Marshal(resp)
	if err != nil {
		c.logger.Error().Err(err).Str("devSN", devSN).Uint32("id", resp.ID).Msg("Failed to marshal setting response")
		return fmt.Errorf("failed to marshal response: %w", err)
	}

	if err := c.Publish(respTopic, respData); err != nil {
		c.logger.Error().Err(err).Str("devSN", devSN).Uint32("id", resp.ID).Msg("Failed to publish setting response")
		return fmt.Errorf("failed to publish response: %w", err)
	}

	c.logger.Info().Str("topic", respTopic).Str("devSN", devSN).Uint32("id", resp.ID).Str("data", resp.Frame).Msg("Published setting response")
	return nil
}

// Close 关闭客户端和请求通道
func (c *Client) Close() {
	if c.reqChan != nil {
		close(c.reqChan)
	}
}
