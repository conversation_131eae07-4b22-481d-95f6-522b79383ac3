# MicroBus Configuration
K8S_ENABLE=true
K8S_MICROBUS_GROUPS=k8s-beech
microbus_groups=beech
KAFKA_BROKERS=localhost:9092
MICROBUS_TOPICS=$system/realdata/heatpump/+/type/+/json
MICROBUS_QUEUE_SIZE=100

# MQTT Configuration
MQTT_BROKER=tcp://localhost:1883
MQTT_CLIENT_ID=beech-forwarder
MQTT_USERNAME=
MQTT_PASSWORD=
MQTT_QOS=0
MQTT_TIMEOUT=30s
MQTT_SUBSCRIBE_TOPICS=foxess/device/setting/v0/+/req

# Hub Configuration
HUB_GROUP=beech
HUB_SET_RESOURCE=$system/hub/set/%s
HUB_GET_RESOURCE=$system/hub/get/%s
HUB_TIMEOUT=30

# Logging Configuration
LOG_LEVEL=debug
LOG_FORMAT=console
