package config

import (
	"os"
	"strconv"
	"strings"
	"time"
)

type Config struct {
	MicroBus MicroBusConfig
	MQTT     MQTTConfig
	Hub      HubConfig
	Log      LogConfig
}

type HubConfig struct {
	Group       string // Hub group name
	SetResource string // Hub set resource pattern
	GetResource string // Hub get resource pattern
	Timeout     int    // Request timeout in seconds
}

type MicroBusConfig struct {
	Brokers   string
	Topics    []string
	QueueSize int
}

type MQTTConfig struct {
	Broker          string
	ClientID        string
	Username        string
	Password        string
	QoS             byte
	Timeout         time.Duration
	SubscribeTopics []string
}

// LogConfig holds logging configuration
type LogConfig struct {
	Level  string
	Format string // json or console
}

// Load loads configuration from environment variables
func Load() (*Config, error) {
	cfg := &Config{
		MicroBus: MicroBusConfig{
			Brokers:   getString("KAFKA_BROKERS", "localhost:9092"),
			Topics:    getStringSlice("MICROBUS_TOPICS", []string{"$system/realdata/+/+/type/+/json"}),
			QueueSize: getInt("MICROBUS_QUEUE_SIZE", 100),
		},
		MQTT: MQTTConfig{
			Broker:          getString("MQTT_BROKER", "tcp://localhost:1883"),
			ClientID:        getString("MQTT_CLIENT_ID", "beech-forwarder-"+strconv.FormatInt(time.Now().Unix(), 10)),
			Username:        getString("MQTT_USERNAME", ""),
			Password:        getString("MQTT_PASSWORD", ""),
			QoS:             byte(getInt("MQTT_QOS", 0)),
			Timeout:         getDuration("MQTT_TIMEOUT", 30*time.Second),
			SubscribeTopics: getStringSlice("MQTT_SUBSCRIBE_TOPICS", []string{"foxess/device/setting/v0/+/req"}),
		},
		Hub: HubConfig{
			Group:       getString("HUB_GROUP", "beech"),
			SetResource: getString("HUB_SET_RESOURCE", "$system/hub/set/%s"),
			GetResource: getString("HUB_GET_RESOURCE", "$system/hub/get/%s"),
			Timeout:     getInt("HUB_TIMEOUT", 30),
		},
		Log: LogConfig{
			Level:  getString("LOG_LEVEL", "info"),
			Format: getString("LOG_FORMAT", "json"),
		},
	}

	return cfg, nil
}

// Helper functions to get environment variables with defaults

func getString(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func getInt(key string, defaultValue int) int {
	if value := os.Getenv(key); value != "" {
		if intValue, err := strconv.Atoi(value); err == nil {
			return intValue
		}
	}
	return defaultValue
}

func getDuration(key string, defaultValue time.Duration) time.Duration {
	if value := os.Getenv(key); value != "" {
		if duration, err := time.ParseDuration(value); err == nil {
			return duration
		}
	}
	return defaultValue
}

func getStringSlice(key string, defaultValue []string) []string {
	if value := os.Getenv(key); value != "" {
		return strings.Split(value, ",")
	}
	return defaultValue
}
